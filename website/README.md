# ContentPal Website

This is the official website for ContentPal (内容君), built with React, Vite, and TailwindCSS.

## Features

- **Multi-language Support**: English, Chinese (中文), and Japanese (日本語)
- **Responsive Design**: Works seamlessly on all devices
- **Privacy Policy Pages**: Compliant with Apple Store Connect requirements
- **Technical Documentation**: Comprehensive user guides and API documentation
- **Modern UI**: Glass morphism effects, animations, and gradient designs

## Development

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

```bash
cd website
npm install
```

### Development Server

```bash
npm run dev
```

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## Tech Stack

- **React 18** - UI framework
- **Vite** - Build tool and development server
- **TailwindCSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **React Helmet Async** - SEO management
- **Lucide React** - Icon library

## Project Structure

```
website/
├── src/
│   ├── components/     # Reusable components
│   │   ├── Header.jsx
│   │   └── Footer.jsx
│   ├── pages/         # Page components
│   │   ├── Home.jsx
│   │   ├── PrivacyPolicy.jsx
│   │   ├── Documentation.jsx
│   │   └── NotFound.jsx
│   ├── assets/        # Static assets
│   ├── locales/       # Translation files
│   ├── utils/         # Utility functions
│   ├── App.jsx        # Main app component
│   ├── index.jsx      # Entry point
│   └── index.css      # Global styles
├── public/            # Public assets
├── index.html         # HTML template
└── package.json       # Dependencies and scripts
```

## Deployment

The website can be deployed to any static hosting service:
- Netlify
- Vercel
- GitHub Pages
- Firebase Hosting

Simply run `npm run build` and deploy the contents of the `dist` folder.

## License

This project is part of the ContentPal application.