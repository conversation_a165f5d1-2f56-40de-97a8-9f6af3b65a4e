# ContentPal Website Deployment Guide

## Quick Deployment Options

### 1. Netlify (Recommended)

#### Method A: Drag & Drop
1. Run `npm run build` in your project directory
2. Drag the `dist` folder to [Netlify Drop](https://app.netlify.com/drop)
3. Your site will be live instantly!

#### Method B: Git Integration
1. Push your code to GitHub
2. Connect your repo to Netlify
3. Configure build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
4. Deploy automatically on every push

### 2. Vercel

#### Method A: CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# For production
vercel --prod
```

#### Method B: Git Integration
1. Push to GitHub/GitLab/Bitbucket
2. Import project to Vercel
3. Auto-detects React + Vite setup
4. Deploys automatically

### 3. GitHub Pages

```bash
# Install gh-pages
npm install --save-dev gh-pages

# Update package.json
{
  "scripts": {
    "predeploy": "npm run build",
    "deploy": "gh-pages -d dist"
  }
}

# Update vite.config.js for base path
export default defineConfig({
  base: '/contentpal/',
  // ... rest of config
})

# Deploy
npm run deploy
```

### 4. Cloudflare Pages

1. Push code to GitHub
2. Connect repo to Cloudflare Pages
3. Build command: `npm run build`
4. Build output directory: `dist`
5. Deploy automatically

## Build and Deploy Commands

### Build for Production
```bash
# Build the website
npm run build

# Preview build locally
npm run preview
```

### Environment Variables (if needed)
```bash
# Netlify
NETlify environment variables through dashboard

# Vercel
vercel env add

# GitHub Pages
No environment variables needed for static sites
```

## Custom Domain Setup

### Netlify
1. Go to Domain settings
2. Add custom domain
3. Update DNS records

### Vercel
1. Project settings → Domains
2. Add domain
3. Follow DNS instructions

### GitHub Pages
1. Repository settings → Pages
2. Set custom domain
3. Update DNS with CNAME or A records

## Optimization Tips

1. **Enable Gzip compression** (most platforms do this automatically)
2. **Set up caching headers** for static assets
3. **Use CDN** for faster global delivery
4. **Enable HTTPS** (automatic on most platforms)

## Troubleshooting

### 404 Errors on Refresh
- Add `_redirects` file in `public` folder for Netlify
- Or configure fallback routing in platform settings

### Routing Issues with Language Paths
- Ensure your hosting platform supports SPA routing
- For GitHub Pages, use hash routing or configure 404 page

### Build Errors
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Check Vite configuration
- Ensure all dependencies are installed

## Deployment Checklist

- [ ] Build runs successfully locally
- [ ] All routes work (home, /en/privacy, /zh/docs, etc.)
- [ ] Language switching works
- [ ] Images and assets load correctly
- [ ] Mobile responsiveness verified
- [ ] Meta tags and SEO are correct
- [ ] Custom domain configured (if using)
- [ ] HTTPS enabled
- [ ] Performance optimized

## Recommended Choice

For quick deployment: **Netlify** (easiest, free, automatic HTTPS, CDN)
For GitHub integration: **Vercel** (great React support, preview deployments)
For free hosting: **GitHub Pages** (no build limits, but limited features)