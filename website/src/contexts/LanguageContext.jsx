import React, { createContext, useContext, useState, useEffect } from 'react'

const LanguageContext = createContext()

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

export const LanguageProvider = ({ children }) => {
  const [currentLang, setCurrentLang] = useState('en')
  
  useEffect(() => {
    // 从URL路径中获取语言
    if (typeof window !== 'undefined') {
      const path = window.location.pathname
      const langMatch = path.match(/^\/([^\/]+)/)
      if (langMatch && ['en', 'zh', 'ja'].includes(langMatch[1])) {
        setCurrentLang(langMatch[1])
      }
    }
  }, [])

  const changeLanguage = (lang) => {
    setCurrentLang(lang)
    // 更新URL但不刷新页面
    if (typeof window !== 'undefined') {
      const path = window.location.pathname
      const currentPath = path.replace(/^\/[a-z]{2}/, '') || '/'
      const newPath = lang === 'en' ? currentPath : `/${lang}${currentPath}`
      window.history.pushState({}, '', newPath)
    }
  }

  const t = (key) => {
    return translations[currentLang]?.[key] || translations.en[key] || key
  }

  return (
    <LanguageContext.Provider value={{ 
      currentLang, 
      changeLanguage, 
      t 
    }}>
      {children}
    </LanguageContext.Provider>
  )
}

// 翻译内容
const translations = {
  en: {
    // 导航
    navHome: 'Home',
    navFeatures: 'Features',
    navPrivacy: 'Privacy Policy',
    navDocs: 'Documentation',
    navLanguage: 'Language',
    
    // 首页
    heroTitle: 'ContentPal',
    heroSubtitle: 'Your Ultimate Content Processing Toolkit',
    heroDescription: 'A powerful all-in-one content processing tool for Markdown, HTML, SVG, PDF, and more. Available now on the App Store.',
    downloadBtn: 'Download on App Store',
    featuresTitle: 'Powerful Features',
    featuresSubtitle: 'Everything you need for content creation and processing',
    
    // 功能特性
    featureMarkdownTitle: 'Markdown Processing',
    featureMarkdownDesc: 'Create and edit Markdown with real-time preview',
    featureSvgTitle: 'SVG Editor',
    featureSvgDesc: 'Powerful vector graphics editor with optimization',
    featureHtmlTitle: 'HTML Editor',
    featureHtmlDesc: 'Professional HTML editor with syntax highlighting',
    featurePdfTitle: 'PDF Tools',
    featurePdfDesc: 'Comprehensive PDF toolkit for all your needs',
    featureCardsTitle: 'Text Cards',
    featureCardsDesc: 'Create beautiful text cards for social media',
    featureGuideTitle: 'Traffic Guide',
    featureGuideDesc: 'Generate professional traffic guide images',
    
    // 页脚
    footerRights: 'All rights reserved.',
    footerPrivacy: 'Privacy Policy',
    footerDocs: 'Documentation',
    
    // 通用
    backToHome: 'Back to Home',
    loading: 'Loading...',
    error: 'An error occurred',
    notFound: 'Page Not Found',
    notFoundDesc: 'The page you\'re looking for doesn\'t exist or has been moved.'
  },
  zh: {
    // 导航
    navHome: '首页',
    navFeatures: '功能特性',
    navPrivacy: '隐私政策',
    navDocs: '技术文档',
    navLanguage: '语言',
    
    // 首页
    heroTitle: '内容君',
    heroSubtitle: '您的内容处理工具包',
    heroDescription: '功能强大的全能内容处理工具，支持 Markdown、HTML、SVG、PDF 等。现已在 App Store 上架。',
    downloadBtn: '在 App Store 下载',
    featuresTitle: '强大功能',
    featuresSubtitle: '内容创作和处理所需的一切',
    
    // 功能特性
    featureMarkdownTitle: 'Markdown 处理',
    featureMarkdownDesc: '使用实时预览创建和编辑 Markdown',
    featureSvgTitle: 'SVG 编辑器',
    featureSvgDesc: '具有优化功能的强大矢量图形编辑器',
    featureHtmlTitle: 'HTML 编辑器',
    featureHtmlDesc: '具有语法高亮的专业 HTML 编辑器',
    featurePdfTitle: 'PDF 工具',
    featurePdfDesc: '满足您所有需求的综合 PDF 工具包',
    featureCardsTitle: '文本卡片',
    featureCardsDesc: '为社交媒体创建精美的文本卡片',
    featureGuideTitle: '交通指南',
    featureGuideDesc: '生成专业的交通指南图像',
    
    // 页脚
    footerRights: '版权所有。',
    footerPrivacy: '隐私政策',
    footerDocs: '技术文档',
    
    // 通用
    backToHome: '返回首页',
    loading: '加载中...',
    error: '发生错误',
    notFound: '页面未找到',
    notFoundDesc: '您查找的页面不存在或已被移动。'
  },
  ja: {
    // 导航
    navHome: 'ホーム',
    navFeatures: '機能',
    navPrivacy: 'プライバシーポリシー',
    navDocs: 'ドキュメント',
    navLanguage: '言語',
    
    // 首页
    heroTitle: 'ContentPal',
    heroSubtitle: '究極のコンテンツ処理ツールキット',
    heroDescription: 'Markdown、HTML、SVG、PDFなどに対応する強力なオールインワンコンテンツ処理ツール。App Storeで利用可能。',
    downloadBtn: 'App Storeでダウンロード',
    featuresTitle: '強力な機能',
    featuresSubtitle: 'コンテンツ作成と処理に必要なすべて',
    
    // 功能特性
    featureMarkdownTitle: 'Markdown処理',
    featureMarkdownDesc: 'リアルタイムプレビューでMarkdownを作成・編集',
    featureSvgTitle: 'SVGエディター',
    featureSvgDesc: '最適化機能を備えた強力なベクターグラフィックエディター',
    featureHtmlTitle: 'HTMLエディター',
    featureHtmlDesc: '構文ハイライトを備えたプロフェッショナルなHTMLエディター',
    featurePdfTitle: 'PDFツール',
    featurePdfDesc: 'すべてのニーズに対応する包括的なPDFツールキット',
    featureCardsTitle: 'テキストカード',
    featureCardsDesc: 'ソーシャルメディア用の美しいテキストカードを作成',
    featureGuideTitle: '交通ガイド',
    featureGuideDesc: 'プロフェッショナルな交通ガイド画像を生成',
    
    // 页脚
    footerRights: 'すべての権利留保。',
    footerPrivacy: 'プライバシーポリシー',
    footerDocs: 'ドキュメント',
    
    // 通用
    backToHome: 'ホームに戻る',
    loading: '読み込み中...',
    error: 'エラーが発生しました',
    notFound: 'ページが見つかりません',
    notFoundDesc: 'お探しのページは存在しないか、移動されました。'
  }
}