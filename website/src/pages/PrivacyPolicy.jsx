import { Helmet } from 'react-helmet-async'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import { ArrowLeft, Shield, Mail, ExternalLink } from 'lucide-react'
import { useLanguage } from '../contexts/LanguageContext'
import { config, getContactInfo } from '../config'

const privacyContent = {
  en: {
    title: 'Privacy Policy',
    effectiveDate: '2025-08-13',
    version: '1.0',
    introduction: 'ContentPal respects your privacy. This policy explains how we handle your information with a local-first, minimal, and transparent approach.',
    sections: [
      {
        title: 'Information We Collect',
        content: [
          'Device/app information: for diagnostics (e.g., crash logs, performance) if enabled in the future.',
          'Local usage data: app settings, subscription state, and usage counters stored locally (Hive) on your device.',
        ],
        notes: [
          'No account sign-in is required; we do not collect personally identifiable information (PII).',
          'Processing is primarily on-device; your content is not uploaded to our servers in the current version.',
        ]
      },
      {
        title: 'How We Use Information',
        content: [
          'Provide and improve features (e.g., export to Photos, template application, subscription entitlements).',
          'Ensure app security and prevent abuse (e.g., local usage limits).',
        ]
      },
      {
        title: 'Data Storage & Retention',
        content: [
          'Local storage: settings, subscription state, usage counters are stored locally (Hive).',
          'Deleting the app or clearing data removes the related records permanently.',
        ]
      },
      {
        title: 'Third-Party Services & Permissions',
        content: [
          'Photos permission: used only when you choose to save images to Photos.',
          'In-App Purchases (IAP): handled via Apple\'s IAP for subscriptions and restore.',
        ]
      },
      {
        title: 'Children\'s Privacy',
        content: [
          'The app targets general users and does not knowingly collect data from children under 13.',
        ]
      },
      {
        title: 'Your Choices & Rights',
        content: [
          'Revoke Photos permission in iOS Settings.',
          'Delete the app to remove local data.',
        ]
      },
      {
        title: 'Changes to This Policy',
        content: [
          'We may update this policy and indicate the new effective date. Significant changes will be communicated in-app or via release notes.',
        ]
      },
      {
        title: 'Contact Us',
        content: [
          `Privacy inquiries: <${getContactInfo().email}>`,
          `Developer website: <${getContactInfo().developerWebsite}>`,
        ]
      }
    ]
  },
  zh: {
    title: '隐私政策',
    effectiveDate: '2025-08-13',
    version: '1.0',
    introduction: '本隐私政策旨在说明内容君（ContentPal）如何处理您的信息。我们重视您的隐私，并尽力以最小化、透明与本地优先的方式处理数据。',
    sections: [
      {
        title: '我们收集的信息',
        content: [
          '设备与应用信息：用于基础诊断（如崩溃日志、性能指标，若未来启用）。',
          '本地使用数据：应用内设置、订阅状态、功能本地使用计数（存储于本地设备 Hive）。',
        ],
        notes: [
          '我们不要求账户登录，不收集可用于识别您个人身份的信息（PII）。',
          '当前版本中，应用核心处理在本地完成，不将您的内容上传至服务器。',
        ]
      },
      {
        title: '我们如何使用信息',
        content: [
          '提供与改进功能（如导出到相册、模板应用、订阅鉴权）。',
          '保障应用安全与防滥用（如本地使用次数限制的计数与校验）。',
        ]
      },
      {
        title: '数据存储与保留',
        content: [
          '本地存储：设置、订阅状态与使用计数保存在本地（Hive）。',
          '如您删除应用或清理本地数据，相关记录将被移除且不可恢复。',
        ]
      },
      {
        title: '第三方服务与权限',
        content: [
          '照片/相册权限：仅在您选择保存图片到相册时使用。',
          '应用内购买（IAP）：通过 Apple 提供的 IAP 接口完成订阅购买与恢复。',
        ]
      },
      {
        title: '儿童隐私',
        content: [
          '本应用面向通用用户，不针对 13 岁以下儿童进行数据收集。',
        ]
      },
      {
        title: '您的权利',
        content: [
          '您可在系统设置中撤销照片权限；',
          '通过删除应用清除本地存储的数据。',
        ]
      },
      {
        title: '变更',
        content: [
          '我们可能更新本政策，并在"生效日期"处标注。重大变更将通过应用内提示或版本更新说明通知。',
        ]
      },
      {
        title: '联系我们',
        content: [
          '隐私相关问题与请求，请联系：<<EMAIL>>',
          '开发者网站：<https://www.jiangkang.tech>',
        ]
      }
    ]
  },
  ja: {
    title: 'プライバシーポリシー',
    effectiveDate: '2025-08-13',
    version: '1.0',
    introduction: 'ContentPalは、あなたのプライバシーを尊重します。このポリシーは、ローカルファースト、最小限、透明性のあるアプローチで情報をどのように扱うかを説明します。',
    sections: [
      {
        title: '収集する情報',
        content: [
          'デバイス/アプリ情報：将来有効にされた場合の診断（クラッシュログ、パフォーマンスなど）。',
          'ローカル使用データ：アプリ設定、サブスクリプション状態、使用カウンターがデバイスにローカル（Hive）に保存されます。',
        ],
        notes: [
          'アカウントサインインは不要です。個人を特定できる情報（PII）は収集しません。',
          '処理は主にデバイス上で行われます。現在のバージョンでは、コンテンツをサーバーにアップロードしません。',
        ]
      },
      {
        title: '情報の使用方法',
        content: [
          '機能の提供と改善（写真へのエクスポート、テンプレートの適用、サブスクリプションの権利など）。',
          'アプリのセキュリティ確保と不正使用の防止（ローカル使用制限など）。',
        ]
      },
      {
        title: 'データ保存と保持',
        content: [
          'ローカル保存：設定、サブスクリプション状態、使用カウンターはローカル（Hive）に保存されます。',
          'アプリを削除するかデータを消去すると、関連レコードは完全に削除されます。',
        ]
      },
      {
        title: 'サードパーティサービスと権限',
        content: [
          '写真権限：写真に画像を保存する場合のみ使用されます。',
          'アプリ内購入（IAP）：AppleのIAPを介してサブスクリプションと復元を処理します。',
        ]
      },
      {
        title: '子供のプライバシー',
        content: [
          'このアプリは一般ユーザーを対象としており、13歳未満の子供から意図的にデータを収集することはありません。',
        ]
      },
      {
        title: '選択肢と権利',
        content: [
          'iOS設定で写真権限を取り消します。',
          'アプリを削除してローカルデータを削除します。',
        ]
      },
      {
        title: 'このポリシーの変更',
        content: [
          'このポリシーを更新し、新しい有効日を示す場合があります。重要な変更は、アプリ内またはリリースノートを通じて通知されます。',
        ]
      },
      {
        title: 'お問い合わせ',
        content: [
          'プライバシーに関するお問い合わせ：<<EMAIL>>',
          '開発者ウェブサイト：<https://www.jiangkang.tech>',
        ]
      }
    ]
  }
}

const PrivacyPolicy = () => {
  const { lang } = useParams()
  const { currentLang, t } = useLanguage()
  const content = privacyContent[lang || currentLang] || privacyContent.en

  return (
    <>
      <Helmet>
        <title>{content.title} - ContentPal</title>
        <meta name="description" content={`ContentPal ${content.title} - Version ${content.version}`} />
      </Helmet>

      <div className="min-h-screen pt-24 pb-12">
        <div className="container mx-auto px-6 max-w-4xl">
          {/* Back Button */}
          <Link 
            to="/" 
            className="inline-flex items-center space-x-2 text-gray-400 hover:text-white transition-colors mb-8"
          >
            <ArrowLeft size={20} />
            <span>{t('backToHome')}</span>
          </Link>

          {/* Header */}
          <div className="glass-effect rounded-2xl p-8 mb-8">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">{content.title}</h1>
                <p className="text-gray-400">
                  {(lang || currentLang) === 'zh' ? '版本' : (lang || currentLang) === 'ja' ? 'バージョン' : 'Version'} {content.version} | {(lang || currentLang) === 'zh' ? '生效日期' : (lang || currentLang) === 'ja' ? '有効日' : 'Effective Date'}: {content.effectiveDate}
                </p>
              </div>
            </div>
            
            <p className="text-gray-300 leading-relaxed">
              {content.introduction}
            </p>
          </div>

          {/* Content Sections */}
          <div className="space-y-6">
            {content.sections.map((section, index) => (
              <div key={index} className="glass-effect rounded-xl p-6">
                <h2 className="text-xl font-semibold text-white mb-4">{section.title}</h2>
                <ul className="space-y-2 text-gray-300">
                  {section.content.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <span className="text-blue-400 mr-3 mt-1">•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
                
                {section.notes && (
                  <div className="mt-4 p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                    <p className="text-sm text-blue-300 mb-2 font-medium">
                      {(lang || currentLang) === 'zh' ? '说明：' : (lang || currentLang) === 'ja' ? '注：' : 'Notes:'}
                    </p>
                    <ul className="space-y-1 text-sm text-gray-300">
                      {section.notes.map((note, noteIndex) => (
                        <li key={noteIndex} className="flex items-start">
                          <span className="text-blue-400 mr-3 mt-1">•</span>
                          <span>{note}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Footer */}
          <div className="mt-12 pt-8 border-t border-white/10 text-center">
            <p className="text-gray-400 mb-4">
              {(lang || currentLang) === 'zh' ? '本隐私政策符合 Apple Store Connect 的要求' : 
               (lang || currentLang) === 'ja' ? 'このプライバシーポリシーはApple Store Connectの要件に準拠しています' :
               'This privacy policy complies with Apple Store Connect requirements'}
            </p>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
              <a 
                href={`mailto:${getContactInfo().email}`} 
                className="flex items-center space-x-2 hover:text-white transition-colors"
              >
                <Mail size={16} />
                <span>{getContactInfo().email}</span>
              </a>
              <a 
                href={getContactInfo().developerWebsite} 
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 hover:text-white transition-colors"
              >
                <ExternalLink size={16} />
                <span>{new URL(getContactInfo().developerWebsite).hostname}</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default PrivacyPolicy