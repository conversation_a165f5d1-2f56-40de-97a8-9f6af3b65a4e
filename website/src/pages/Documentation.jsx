import { Helmet } from 'react-helmet-async'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { ArrowLeft, BookOpen, Code, Settings, Download, Share, Palette, FileText, Image as ImageIcon, Zap } from 'lucide-react'
import { useLanguage } from '../contexts/LanguageContext'
import { config, getContactInfo } from '../config'

const docContent = {
  en: {
    title: 'Documentation',
    subtitle: 'Technical Documentation & User Guide',
    sections: [
      {
        title: 'Getting Started',
        icon: Zap,
        items: [
          {
            title: 'Installation',
            content: 'Download ContentPal from the App Store. The app requires iOS 15.0 or later and is optimized for iPhone and iPad.',
          },
          {
            title: 'First Launch',
            content: 'When you first launch ContentPal, you\'ll be greeted with the home screen showing all available content processing tools. No registration or account creation is required.',
          },
          {
            title: 'Permissions',
            content: 'ContentPal only requests permissions when necessary. Photo library access is only needed when exporting images.',
          }
        ]
      },
      {
        title: 'Features',
        icon: Palette,
        items: [
          {
            title: 'Markdown Processing',
            content: 'Create and edit Markdown documents with real-time preview. Support for tables, code blocks, mathematical formulas, and custom styling.',
            features: ['Real-time preview', 'Block-based editing', 'Export to PDF/HTML/Image', 'Custom templates', 'Watermark support']
          },
          {
            title: 'SVG Editor',
            content: 'Powerful SVG icon editor with optimization tools. Perfect for creating and editing vector graphics.',
            features: ['SVG optimization', 'Color editing', 'Size adjustment', 'Batch processing', 'Export to PNG/JPG']
          },
          {
            title: 'HTML Editor',
            content: 'Professional HTML editor with syntax highlighting and live preview. Ideal for web developers.',
            features: ['Syntax highlighting', 'Live preview', 'Code formatting', 'Tag completion', 'Error checking']
          },
          {
            title: 'PDF Tools',
            content: 'Comprehensive PDF toolkit for viewing, annotating, and securing documents.',
            features: ['PDF viewer', 'Annotations', 'Password protection', 'Watermarking', 'Page management']
          },
          {
            title: 'Text Cards',
            content: 'Create beautiful text cards for social media and presentations with smart templates.',
            features: ['Multiple templates', 'Smart text splitting', 'Rich text editing', 'Export options', 'Custom styling']
          },
          {
            title: 'Traffic Guide',
            content: 'Generate professional traffic guide images with customizable templates and real-time preview.',
            features: ['Template gallery', 'Real-time preview', 'Custom dimensions', 'Text styling', 'Export options']
          }
        ]
      },
      {
        title: 'Data Management',
        icon: Settings,
        items: [
          {
            title: 'Local Storage',
            content: 'All your data is stored locally on your device using Hive database. No data is sent to external servers.',
          },
          {
            title: 'Backup & Restore',
            content: 'Currently, ContentPal doesn\'t support cloud backup. All data remains on your device for privacy.',
          },
          {
            title: 'Export Options',
            content: 'Export your content in various formats: PDF, PNG, JPG, HTML, and plain text.',
          }
        ]
      },
      {
        title: 'Subscription',
        icon: Download,
        items: [
          {
            title: 'Free Features',
            content: 'Basic features are available for free with no time limits.',
          },
          {
            title: 'Premium Features',
            content: 'Unlock advanced features with a subscription. Managed through Apple\'s In-App Purchase system.',
          },
          {
            title: 'Subscription Management',
            content: 'Manage your subscription through iOS Settings > Apple ID > Subscriptions.',
          }
        ]
      },
      {
        title: 'Privacy & Security',
        icon: FileText,
        items: [
          {
            title: 'Privacy-First',
            content: 'ContentPal processes all data locally. Your content never leaves your device.',
          },
          {
            title: 'No Tracking',
            content: 'We don\'t use analytics, tracking, or advertising SDKs.',
          },
          {
            title: 'Data Encryption',
            content: 'Sensitive data is encrypted using industry-standard encryption algorithms.',
          }
        ]
      },
      {
        title: 'Technical Details',
        icon: Code,
        items: [
          {
            title: 'Technologies',
            content: 'Built with Flutter 3.7.2+, using Dart for business logic and native platform integrations.',
          },
          {
            title: 'Architecture',
            content: 'Modular architecture with clean separation of concerns. Service Locator pattern for dependency injection.',
          },
          {
            title: 'Performance',
            content: 'Optimized for smooth performance with lazy loading and efficient memory management.',
          }
        ]
      }
    ],
    footer: {
      support: `For technical support, please contact: ${getContactInfo().email}`,
      website: `Developer website: ${getContactInfo().developerWebsite}`,
      version: 'Documentation Version: 1.0'
    }
  },
  zh: {
    title: '技术文档',
    subtitle: '技术文档与用户指南',
    sections: [
      {
        title: '快速开始',
        icon: Zap,
        items: [
          {
            title: '安装',
            content: '从 App Store 下载内容君。应用需要 iOS 15.0 或更高版本，已针对 iPhone 和 iPad 进行优化。',
          },
          {
            title: '首次启动',
            content: '首次启动内容君时，您将看到显示所有可用内容处理工具的主屏幕。无需注册或创建账户。',
          },
          {
            title: '权限',
            content: '内容君仅在必要时请求权限。仅在导出图像时才需要照片库访问权限。',
          }
        ]
      },
      {
        title: '功能特性',
        icon: Palette,
        items: [
          {
            title: 'Markdown 处理',
            content: '使用实时预览创建和编辑 Markdown 文档。支持表格、代码块、数学公式和自定义样式。',
            features: ['实时预览', '分块编辑', '导出为 PDF/HTML/图片', '自定义模板', '水印支持']
          },
          {
            title: 'SVG 编辑器',
            content: '具有优化功能的强大 SVG 图标编辑器。非常适合创建和编辑矢量图形。',
            features: ['SVG 优化', '颜色编辑', '尺寸调整', '批量处理', '导出为 PNG/JPG']
          },
          {
            title: 'HTML 编辑器',
            content: '具有语法高亮和实时预览的专业 HTML 编辑器。是 Web 开发者的理想选择。',
            features: ['语法高亮', '实时预览', '代码格式化', '标签补全', '错误检查']
          },
          {
            title: 'PDF 工具',
            content: '用于查看、注释和保护文档的综合 PDF 工具包。',
            features: ['PDF 查看器', '注释功能', '密码保护', '水印', '页面管理']
          },
          {
            title: '文本卡片',
            content: '使用智能模板为社交媒体和演示文稿创建精美的文本卡片。',
            features: ['多种模板', '智能文本分割', '富文本编辑', '导出选项', '自定义样式']
          },
          {
            title: '交通指南',
            content: '使用可自定义模板和实时预览生成专业的交通指南图像。',
            features: ['模板库', '实时预览', '自定义尺寸', '文本样式', '导出选项']
          }
        ]
      },
      {
        title: '数据管理',
        icon: Settings,
        items: [
          {
            title: '本地存储',
            content: '所有数据都使用 Hive 数据库本地存储在您的设备上。不会向外部服务器发送任何数据。',
          },
          {
            title: '备份与恢复',
            content: '目前，内容君不支持云备份。所有数据都保留在您的设备上以保护隐私。',
          },
          {
            title: '导出选项',
            content: '以各种格式导出您的内容：PDF、PNG、JPG、HTML 和纯文本。',
          }
        ]
      },
      {
        title: '订阅',
        icon: Download,
        items: [
          {
            title: '免费功能',
            content: '基本功能可免费使用，没有时间限制。',
          },
          {
            title: '高级功能',
            content: '通过订阅解锁高级功能。通过 Apple 的应用内购买系统管理。',
          },
          {
            title: '订阅管理',
            content: '通过 iOS 设置 > Apple ID > 订阅管理您的订阅。',
          }
        ]
      },
      {
        title: '隐私与安全',
        icon: FileText,
        items: [
          {
            title: '隐私优先',
            content: '内容君在本地处理所有数据。您的内容永远不会离开您的设备。',
          },
          {
            title: '无追踪',
            content: '我们不使用分析、追踪或广告 SDK。',
          },
          {
            title: '数据加密',
            content: '敏感数据使用行业标准加密算法进行加密。',
          }
        ]
      },
      {
        title: '技术细节',
        icon: Code,
        items: [
          {
            title: '技术栈',
            content: '使用 Flutter 3.7.2+ 构建，使用 Dart 进行业务逻辑和原生平台集成。',
          },
          {
            title: '架构',
            content: '模块化架构，关注点清晰分离。使用服务定位器模式进行依赖注入。',
          },
          {
            title: '性能',
            content: '通过延迟加载和高效的内存管理优化，确保流畅的性能。',
          }
        ]
      }
    ],
    footer: {
      support: `技术支持请联系：${getContactInfo().email}`,
      website: `开发者网站：${getContactInfo().developerWebsite}`,
      version: '文档版本：1.0'
    }
  },
  ja: {
    title: 'ドキュメント',
    subtitle: '技術ドキュメントとユーザーガイド',
    sections: [
      {
        title: 'クイックスタート',
        icon: Zap,
        items: [
          {
            title: 'インストール',
            content: 'App StoreからContentPalをダウンロードします。アプリにはiOS 15.0以降が必要で、iPhoneとiPadに最適化されています。',
          },
          {
            title: '初回起動',
            content: 'ContentPalを初めて起動すると、利用可能なすべてのコンテンツ処理ツールを表示するホーム画面が表示されます。登録やアカウント作成は不要です。',
          },
          {
            title: '権限',
            content: 'ContentPalは必要な場合のみ権限を要求します。写真ライブラリアクセスは、画像をエクスポートする場合にのみ必要です。',
          }
        ]
      },
      {
        title: '機能',
        icon: Palette,
        items: [
          {
            title: 'Markdown処理',
            content: 'リアルタイムプレビューでMarkdownドキュメントを作成・編集します。表、コードブロック、数式、カスタムスタイリングをサポート。',
            features: ['リアルタイムプレビュー', 'ブロックベース編集', 'PDF/HTML/画像へのエクスポート', 'カスタムテンプレート', 'ウォーターマークサポート']
          },
          {
            title: 'SVGエディター',
            content: '最適化ツールを備えた強力なSVGアイコンエディター。ベクターグラフィックの作成と編集に最適です。',
            features: ['SVG最適化', 'カラー編集', 'サイズ調整', 'バッチ処理', 'PNG/JPGへのエクスポート']
          },
          {
            title: 'HTMLエディター',
            content: '構文ハイライトとライブプレビューを備えたプロフェッショナルなHTMLエディター。Web開発者に最適です。',
            features: ['構文ハイライト', 'ライブプレビュー', 'コードフォーマット', 'タグ補完', 'エラーチェック']
          },
          {
            title: 'PDFツール',
            content: 'ドキュメントの表示、注釈、セキュリティ保護のための包括的なPDFツールキット。',
            features: ['PDFビューア', '注釈機能', 'パスワード保護', 'ウォーターマーク', 'ページ管理']
          },
          {
            title: 'テキストカード',
            content: 'スマートテンプレートを使用して、ソーシャルメディアやプレゼンテーション用の美しいテキストカードを作成します。',
            features: ['複数のテンプレート', 'スマートテキスト分割', 'リッチテキスト編集', 'エクスポートオプション', 'カスタムスタイリング']
          },
          {
            title: '交通ガイド',
            content: 'カスタマイズ可能なテンプレートとリアルタイムプレビューで、プロフェッショナルな交通ガイド画像を生成します。',
            features: ['テンプレートギャラリー', 'リアルタイムプレビュー', 'カスタム寸法', 'テキストスタイリング', 'エクスポートオプション']
          }
        ]
      },
      {
        title: 'データ管理',
        icon: Settings,
        items: [
          {
            title: 'ローカルストレージ',
            content: 'すべてのデータはHiveデータベースを使用してデバイスにローカルに保存されます。外部サーバーにデータは送信されません。',
          },
          {
            title: 'バックアップと復元',
            content: '現在、ContentPalはクラウドバックアップをサポートしていません。すべてのデータはプライバシーのためにデバイスに残ります。',
          },
          {
            title: 'エクスポートオプション',
            content: 'PDF、PNG、JPG、HTML、プレーンテキストなど、さまざまな形式でコンテンツをエクスポートします。',
          }
        ]
      },
      {
        title: 'サブスクリプション',
        icon: Download,
        items: [
          {
            title: '無料機能',
            content: '基本的な機能は時間制限なしで無料で利用できます。',
          },
          {
            title: 'プレミアム機能',
            content: 'サブスクリプションで高度な機能をロック解除します。Appleのアプリ内購入システムを介して管理されます。',
          },
          {
            title: 'サブスクリプション管理',
            content: 'iOS設定 > Apple ID > サブスクリプションでサブスクリプションを管理します。',
          }
        ]
      },
      {
        title: 'プライバシーとセキュリティ',
        icon: FileText,
        items: [
          {
            title: 'プライバシー第一',
            content: 'ContentPalはすべてのデータをローカルで処理します。コンテンツがデバイスを離れることはありません。',
          },
          {
            title: '追跡なし',
            content: '分析、追跡、または広告SDKを使用していません。',
          },
          {
            title: 'データ暗号化',
            content: '機密データは業界標準の暗号化アルゴリズムを使用して暗号化されます。',
          }
        ]
      },
      {
        title: '技術詳細',
        icon: Code,
        items: [
          {
            title: 'テクノロジー',
            content: 'Flutter 3.7.2+で構築され、Dartを使用してビジネスロジックとネイティブプラットフォーム統合を行います。',
          },
          {
            title: 'アーキテクチャ',
            content: '関心の分離を明確にしたモジュラーアーキテクチャ。依存性注入にはサービスロケーターパターンを使用します。',
          },
          {
            title: 'パフォーマンス',
            content: '遅延読み込みと効率的なメモリ管理で、スムーズなパフォーマンスになるように最適化されています。',
          }
        ]
      }
    ],
    footer: {
      support: `技術サポートについては、${getContactInfo().email} までお問い合わせください`,
      website: `開発者ウェブサイト：${getContactInfo().developerWebsite}`,
      version: 'ドキュメントバージョン：1.0'
    }
  }
}

const Documentation = () => {
  const { lang } = useParams()
  const { currentLang, t } = useLanguage()
  const content = docContent[lang || currentLang] || docContent.en

  return (
    <>
      <Helmet>
        <title>{content.title} - ContentPal</title>
        <meta name="description" content={content.subtitle} />
      </Helmet>

      <div className="min-h-screen pt-24 pb-12">
        <div className="container mx-auto px-6 max-w-6xl">
          {/* Back Button */}
          <Link 
            to="/" 
            className="inline-flex items-center space-x-2 text-gray-400 hover:text-white transition-colors mb-8"
          >
            <ArrowLeft size={20} />
            <span>{t('backToHome')}</span>
          </Link>

          {/* Header */}
          <div className="glass-effect rounded-2xl p-8 mb-8">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">{content.title}</h1>
                <p className="text-gray-400">{content.subtitle}</p>
              </div>
            </div>
          </div>

          {/* Documentation Sections */}
          <div className="space-y-8">
            {content.sections.map((section, index) => (
              <div key={index} className="glass-effect rounded-xl p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <section.icon className="w-8 h-8 text-blue-400" />
                  <h2 className="text-2xl font-semibold text-white">{section.title}</h2>
                </div>

                <div className="space-y-6">
                  {section.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="bg-white/5 rounded-lg p-6">
                      <h3 className="text-xl font-medium text-white mb-3">{item.title}</h3>
                      <p className="text-gray-300 mb-4 leading-relaxed">{item.content}</p>
                      
                      {item.features && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                          {item.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-center space-x-2 text-sm text-gray-400">
                              <Share size={14} className="text-blue-400" />
                              <span>{feature}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Footer */}
          <div className="mt-12 pt-8 border-t border-white/10 text-center">
            <p className="text-gray-400 mb-2">{content.footer.support}</p>
            <p className="text-gray-400 mb-2">{content.footer.website}</p>
            <p className="text-gray-500 text-sm">{content.footer.version}</p>
          </div>
        </div>
      </div>
    </>
  )
}

export default Documentation