import { Helmet } from 'react-helmet-async'
import { ArrowRight, Download, Star, CheckCircle, Play } from 'lucide-react'
import { useLanguage } from '../contexts/LanguageContext'
import { config, getLocalizedText, getStoreUrls } from '../config'
import Screenshots from '../components/Screenshots'

const Home = () => {
  const { currentLang, t } = useLanguage()
  
  // Get features from config
  const features = config.features.map(feature => ({
    ...feature,
    title: t(`feature${feature.key.charAt(0).toUpperCase() + feature.key.slice(1)}Title`),
    description: t(`feature${feature.key.charAt(0).toUpperCase() + feature.key.slice(1)}Desc`)
  }))

  // Get store URLs
  const storeUrls = getStoreUrls()

  const platforms = [
    { name: 'iOS', icon: '🍎', description: 'Available on App Store' },
    { name: 'Android', icon: '🤖', description: 'Coming soon to Google Play' },
    { name: 'Web', icon: '🌐', description: 'Web version in development' },
  ]

  return (
    <>
      <Helmet>
        <title>{getLocalizedText(config.seo.title, currentLang)}</title>
        <meta name="description" content={getLocalizedText(config.seo.description, currentLang)} />
        <meta name="keywords" content={getLocalizedText(config.seo.keywords, currentLang)} />
        <meta property="og:title" content={getLocalizedText(config.seo.title, currentLang)} />
        <meta property="og:description" content={getLocalizedText(config.seo.description, currentLang)} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://contentpal.com" />
      </Helmet>

      {/* Hero Section */}
      <section className="min-h-screen flex items-center justify-center relative overflow-hidden pt-20">
        {/* Animated Background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
          <div className="absolute -bottom-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/2 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
        </div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fade-in">
              <span className="gradient-text">{currentLang === 'zh' ? '内容君' : 'ContentPal'}</span>
              {currentLang === 'en' && <><br /><span className="text-white">内容君</span></>}
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 animate-slide-up">
              {t('heroSubtitle')}
            </p>
            <p className="text-lg text-gray-400 mb-12 max-w-2xl mx-auto animate-slide-up" style={{animationDelay: '0.1s'}}>
              {t('heroDescription')}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up" style={{animationDelay: '0.2s'}}>
              <a
                href={storeUrls.appleStore}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-full font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center space-x-2"
              >
                <Download size={20} />
                <span>{t('downloadBtn')}</span>
              </a>
              <a
                href="#features"
                className="glass-effect text-white px-8 py-4 rounded-full font-semibold hover:bg-white/20 transition-all duration-300 flex items-center space-x-2"
              >
                <Play size={20} />
                <span>{currentLang === 'zh' ? '观看演示' : currentLang === 'ja' ? 'デモを見る' : 'Watch Demo'}</span>
              </a>
            </div>

            {/* App Store Badges */}
            <div className="mt-12 flex justify-center items-center space-x-6 animate-slide-up" style={{animationDelay: '0.3s'}}>
              <a
                href={storeUrls.appleStore}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-black/50 backdrop-blur-sm rounded-lg px-6 py-3 flex items-center space-x-3 hover:bg-black/70 transition-colors"
              >
                <span className="text-2xl">🍎</span>
                <div className="text-left">
                  <p className="text-xs text-gray-400">{currentLang === 'zh' ? '下载自' : currentLang === 'ja' ? 'ダウンロード' : 'Download on the'}</p>
                  <p className="text-white font-semibold">App Store</p>
                </div>
              </a>
              {storeUrls.googlePlay && (
                <a
                  href={storeUrls.googlePlay}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-black/50 backdrop-blur-sm rounded-lg px-6 py-3 flex items-center space-x-3 hover:bg-black/70 transition-colors"
                >
                  <span className="text-2xl">🤖</span>
                  <div className="text-left">
                    <p className="text-xs text-gray-400">{currentLang === 'zh' ? '下载自' : currentLang === 'ja' ? 'ダウンロード' : 'Get it on'}</p>
                    <p className="text-white font-semibold">Google Play</p>
                  </div>
                </a>
              )}
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              {t('featuresTitle')}
            </h2>
            <p className="text-xl text-gray-400">
              {t('featuresSubtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={feature.title}
                className={`feature-card animate-slide-up ${feature.color}`}
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <feature.icon className="w-12 h-12 mb-4 text-white" />
                <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                <p className="text-gray-400">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Screenshots Section - Replaced with component */}
      <Screenshots />

      {/* Platforms Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Available Everywhere
            </h2>
            <p className="text-xl text-gray-400">
              Access your content across all devices
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {platforms.map((platform) => (
              <div key={platform.name} className="text-center">
                <div className="text-6xl mb-4">{platform.icon}</div>
                <h3 className="text-2xl font-semibold text-white mb-2">{platform.name}</h3>
                <p className="text-gray-400">{platform.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section id="download" className="py-20">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <div className="glass-effect rounded-3xl p-12">
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Ready to Get Started?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Join thousands of users who are already creating amazing content with ContentPal
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href={storeUrls.appleStore}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-full font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center justify-center space-x-2"
                >
                  <span className="text-2xl">🍎</span>
                  <span>{currentLang === 'zh' ? '在 App Store 下载' : currentLang === 'ja' ? 'App Storeでダウンロード' : 'Download on App Store'}</span>
                </a>
                {storeUrls.googlePlay ? (
                  <a
                    href={storeUrls.googlePlay}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-gradient-to-r from-green-600 to-teal-600 text-white px-8 py-4 rounded-full font-semibold hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center justify-center space-x-2"
                  >
                    <span className="text-2xl">🤖</span>
                    <span>{currentLang === 'zh' ? '在 Google Play 下载' : currentLang === 'ja' ? 'Google Playでダウンロード' : 'Get it on Google Play'}</span>
                  </a>
                ) : (
                  <a
                    href="#"
                    className="glass-effect text-white px-8 py-4 rounded-full font-semibold hover:bg-white/20 transition-all duration-300 flex items-center justify-center"
                  >
                    <span>{currentLang === 'zh' ? '🔔 通知我 Android 版本' : currentLang === 'ja' ? '🔔 Android版を通知する' : '🔔 Notify Me for Android'}</span>
                  </a>
                )}
              </div>

              <div className="mt-8 flex items-center justify-center space-x-6 text-gray-400">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>No registration required</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Local-first processing</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Privacy focused</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

export default Home