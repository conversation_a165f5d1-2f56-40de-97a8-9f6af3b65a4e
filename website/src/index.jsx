import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { He<PERSON>etProvider } from 'react-helmet-async'
import App from './App.jsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <HelmetProvider>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </HelmetProvider>
  </React.StrictMode>,
)