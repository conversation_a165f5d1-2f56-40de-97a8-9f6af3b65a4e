import { Link } from 'react-router-dom'
import { useLanguage } from '../contexts/LanguageContext'
import { config, getLocalizedText, getContactInfo } from '../config'

const Footer = () => {
  const currentYear = new Date().getFullYear()
  const { currentLang, t } = useLanguage()
  
  // Get contact info from config
  const contactInfo = getContactInfo()

  const footerLinks = {
    [currentLang === 'zh' ? '产品' : currentLang === 'ja' ? '製品' : 'Product']: [
      { name: t('navFeatures'), href: '#features' },
      { name: 'Screenshots', href: '#screenshots' },
      { name: 'Download', href: '#download' },
    ],
    [currentLang === 'zh' ? '法律' : currentLang === 'ja' ? '法務' : 'Legal']: [
      { name: t('footerPrivacy') + ' (EN)', href: config.legal.privacyPolicy.en },
      { name: '隐私政策 (中文)', href: config.legal.privacyPolicy.zh },
      { name: 'プライバシーポリシー (日本語)', href: config.legal.privacyPolicy.ja },
    ],
    [currentLang === 'zh' ? '支持' : currentLang === 'ja' ? 'サポート' : 'Support']: [
      { name: t('footerDocs') + ' (EN)', href: config.legal.documentation.en },
      { name: '技术文档 (中文)', href: config.legal.documentation.zh },
      { name: 'ドキュメント (日本語)', href: config.legal.documentation.ja },
      { name: currentLang === 'zh' ? '联系我们' : currentLang === 'ja' ? 'お問い合わせ' : 'Contact', href: `mailto:${contactInfo.email}` },
    ],
  }

  return (
    <footer className="bg-black/50 backdrop-blur-sm border-t border-white/10 mt-20">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Logo and Description */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">C</span>
              </div>
              <span className="text-white font-bold text-xl">{currentLang === 'zh' ? config.app.nameZh : config.app.name}</span>
            </div>
            <p className="text-gray-400 mb-4">
              {getLocalizedText(config.app.description, currentLang)}
            </p>
            <p className="text-gray-500 text-sm">
              © {currentYear} {currentLang === 'zh' ? '内容君' : 'ContentPal'}. {t('footerRights')}
            </p>
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h3 className="text-white font-semibold mb-4">{category}</h3>
              <ul className="space-y-2">
                {links.map((link) => (
                  <li key={link.name}>
                    <Link
                      to={link.href}
                      className="text-gray-400 hover:text-white transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mt-8 pt-8 border-t border-white/10 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-500 text-sm mb-4 md:mb-0">
            Made with ❤️ by Jiang Kang
          </p>
          <div className="flex space-x-6">
            <a
              href={contactInfo.developerWebsite}
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-white transition-colors"
            >
              {currentLang === 'zh' ? '开发者网站' : currentLang === 'ja' ? '開発者サイト' : 'Developer Website'}
            </a>
            {contactInfo.github && (
              <a
                href={contactInfo.github}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                GitHub
              </a>
            )}
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer