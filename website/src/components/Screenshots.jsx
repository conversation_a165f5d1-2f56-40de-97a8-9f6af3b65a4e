import { useLanguage } from '../contexts/LanguageContext'
import { config, getLocalizedText } from '../config'

const Screenshots = () => {
  const { currentLang } = useLanguage()

  return (
    <section id="screenshots" className="py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">
            {currentLang === 'zh' ? '应用截图' : 
             currentLang === 'ja' ? 'アプリスクリーンショット' : 
             'App Screenshots'}
          </h2>
          <p className="text-xl text-gray-400">
            {currentLang === 'zh' ? '查看内容君的界面和功能' : 
             currentLang === 'ja' ? 'ContentPalのインターフェースと機能をご覧ください' : 
             'Take a look at ContentPal\'s interface and features'}
          </p>
        </div>

        <div className="screenshots-container">
          {config.screenshots.map((screenshot) => (
            <div key={screenshot.id} className="screenshot-item">
              <div className="screenshot-frame">
                <img 
                  src={`/assets/${screenshot.id}.png`}
                  alt={getLocalizedText(screenshot.alt, currentLang)}
                  className="screenshot-img"
                  loading="lazy"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Screenshots