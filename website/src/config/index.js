// Centralized configuration for ContentPal website
export const config = {
  // App Information
  app: {
    name: 'ContentPal',
    nameZh: '内容君',
    version: '1.0.0',
    description: {
      en: 'A powerful content processing tool for creators and professionals. Edit, convert, and enhance your content with ease.',
      zh: '为创作者和专业人士提供强大的内容处理工具。轻松编辑、转换和增强您的内容。',
      ja: 'クリエイターと専門家のための強力なコンテンツ処理ツール。コンテンツを簡単に編集、変換、強化。'
    }
  },

  // Store Links
  stores: {
    appleStore: {
      url: 'https://apps.apple.com/app/contentpal/id1234567890',
      badge: '/assets/app-store-badge.svg'
    },
    googlePlay: {
      url: 'https://play.google.com/store/apps/details?id=com.contentpal.app',
      badge: '/assets/google-play-badge.svg'
    },
    webVersion: {
      url: 'https://web.contentpal.com',
      available: false // Set to true when web version is ready
    }
  },

  // Contact Information
  contact: {
    email: '<EMAIL>',
    developerWebsite: 'https://www.jiangkang.tech',
    github: 'https://github.com/jiangkang/contentpal'
  },

  // Social Media Links
  social: {
    twitter: 'https://twitter.com/contentpal',
    instagram: 'https://instagram.com/contentpal',
    linkedin: 'https://linkedin.com/company/contentpal'
  },

  // Legal Pages
  legal: {
    privacyPolicy: {
      en: '/en/privacy',
      zh: '/zh/privacy',
      ja: '/ja/privacy'
    },
    documentation: {
      en: '/en/docs',
      zh: '/zh/docs',
      ja: '/ja/docs'
    }
  },

  // Screenshots
  screenshots: [
    {
      id: 1,
      alt: {
        en: 'ContentPal Markdown Editor',
        zh: '内容君 Markdown 编辑器',
        ja: 'ContentPal Markdownエディター'
      }
    },
    {
      id: 2,
      alt: {
        en: 'ContentPal SVG Editor',
        zh: '内容君 SVG 编辑器',
        ja: 'ContentPal SVGエディター'
      }
    },
    {
      id: 3,
      alt: {
        en: 'ContentPal PDF Tools',
        zh: '内容君 PDF 工具',
        ja: 'ContentPal PDFツール'
      }
    },
    {
      id: 4,
      alt: {
        en: 'ContentPal Text Cards',
        zh: '内容君 文本卡片',
        ja: 'ContentPalテキストカード'
      }
    },
    {
      id: 5,
      alt: {
        en: 'ContentPal Traffic Guide',
        zh: '内容君 交通指南',
        ja: 'ContentPal交通ガイド'
      }
    },
    {
      id: 6,
      alt: {
        en: 'ContentPal Settings',
        zh: '内容君 设置',
        ja: 'ContentPal設定'
      }
    },
    {
      id: 7,
      alt: {
        en: 'ContentPal Export Options',
        zh: '内容君 导出选项',
        ja: 'ContentPalエクスポートオプション'
      }
    },
    {
      id: 8,
      alt: {
        en: 'ContentPal Subscription',
        zh: '内容君 订阅',
        ja: 'ContentPalサブスクリプション'
      }
    }
  ],

  // Features Configuration
  features: [
    {
      icon: 'Edit',
      key: 'markdown',
      color: 'from-blue-500 to-purple-600'
    },
    {
      icon: 'Image',
      key: 'svg',
      color: 'from-green-500 to-teal-600'
    },
    {
      icon: 'Code',
      key: 'html',
      color: 'from-orange-500 to-red-600'
    },
    {
      icon: 'FileText',
      key: 'pdf',
      color: 'from-red-500 to-pink-600'
    },
    {
      icon: 'Layout',
      key: 'cards',
      color: 'from-purple-500 to-indigo-600'
    },
    {
      icon: 'Navigation',
      key: 'traffic',
      color: 'from-yellow-500 to-orange-600'
    }
  ],

  // SEO Configuration
  seo: {
    title: {
      en: 'ContentPal - Powerful Content Processing Tool',
      zh: '内容君 - 强大的内容处理工具',
      ja: 'ContentPal - 強力なコンテンツ処理ツール'
    },
    description: {
      en: 'Edit, convert, and enhance your content with ContentPal. Markdown, SVG, HTML, PDF tools, and more.',
      zh: '使用内容君编辑、转换和增强您的内容。Markdown、SVG、HTML、PDF工具等。',
      ja: 'ContentPalでコンテンツを編集、変換、強化。Markdown、SVG、HTML、PDFツールなど。'
    },
    keywords: {
      en: 'content editor, markdown, svg, html, pdf, text processing, productivity app',
      zh: '内容编辑器, markdown, svg, html, pdf, 文本处理, 效率应用',
      ja: 'コンテンツエディター, markdown, svg, html, pdf, テキスト処理, 生産性アプリ'
    }
  }
}

// Helper function to get localized text
export const getLocalizedText = (obj, lang = 'en') => {
  return obj[lang] || obj.en || ''
}

// Helper function to get store URLs
export const getStoreUrls = () => {
  return {
    appleStore: config.stores.appleStore.url,
    googlePlay: config.stores.googlePlay.url,
    webVersion: config.stores.webVersion.available ? config.stores.webVersion.url : null
  }
}

// Helper function to get contact info
export const getContactInfo = () => {
  return {
    email: config.contact.email,
    developerWebsite: config.contact.developerWebsite,
    github: config.contact.github
  }
}