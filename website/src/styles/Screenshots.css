/* Screenshots Gallery Styles */
.screenshots-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.screenshot-item {
  position: relative;
  transform: translateY(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.screenshot-item:hover {
  transform: translateY(-10px);
}

.screenshot-frame {
  position: relative;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 24px;
  padding: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  overflow: hidden;
}

.screenshot-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  border-radius: 20px;
  pointer-events: none;
}

.screenshot-img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 12px;
  position: relative;
  z-index: 1;
}

/* Device frame effect */
.screenshot-frame::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

/* Animation for screenshots */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.screenshot-item {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

.screenshot-item:nth-child(1) { animation-delay: 0.1s; }
.screenshot-item:nth-child(2) { animation-delay: 0.2s; }
.screenshot-item:nth-child(3) { animation-delay: 0.3s; }
.screenshot-item:nth-child(4) { animation-delay: 0.4s; }
.screenshot-item:nth-child(5) { animation-delay: 0.5s; }
.screenshot-item:nth-child(6) { animation-delay: 0.6s; }
.screenshot-item:nth-child(7) { animation-delay: 0.7s; }
.screenshot-item:nth-child(8) { animation-delay: 0.8s; }

/* Responsive adjustments */
@media (max-width: 768px) {
  .screenshots-container {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .screenshots-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .screenshot-frame {
    padding: 8px;
  }
}