# Configuration Guide

This document explains how to use the centralized configuration system in the ContentPal website.

## Overview

The configuration system is located in `src/config/index.js` and centralizes all frequently updated information such as:

- App store links
- Contact information
- Social media links
- Legal page URLs
- Screenshots configuration
- SEO metadata
- Feature definitions

## Configuration Structure

### App Information
```javascript
app: {
  name: 'ContentPal',
  nameZh: '内容君',
  version: '1.0.0',
  description: { en: '...', zh: '...', ja: '...' }
}
```

### Store Links
```javascript
stores: {
  appleStore: {
    url: 'https://apps.apple.com/app/contentpal/id1234567890',
    badge: '/assets/app-store-badge.svg'
  },
  googlePlay: {
    url: 'https://play.google.com/store/apps/details?id=com.contentpal.app',
    badge: '/assets/google-play-badge.svg'
  },
  webVersion: {
    url: 'https://web.contentpal.com',
    available: false
  }
}
```

### Contact Information
```javascript
contact: {
  email: '<EMAIL>',
  developerWebsite: 'https://www.jiangkang.tech',
  github: 'https://github.com/jiangkang/contentpal'
}
```

## Helper Functions

### getLocalizedText(obj, lang)
Gets localized text from an object with language keys.

```javascript
import { getLocalizedText } from '../config'

// Usage
const title = getLocalizedText(config.app.description, 'zh') // Returns Chinese description
```

### getStoreUrls()
Returns all store URLs.

```javascript
import { getStoreUrls } from '../config'

// Usage
const { appleStore, googlePlay, webVersion } = getStoreUrls()
```

### getContactInfo()
Returns contact information.

```javascript
import { getContactInfo } from '../config'

// Usage
const { email, developerWebsite, github } = getContactInfo()
```

## Updating Configuration

### When to Update
- App store URLs change
- Contact information changes
- New screenshots are added
- SEO metadata needs updating
- New features are added
- Social media links change

### How to Update
1. Open `src/config/index.js`
2. Locate the section you need to update
3. Make your changes
4. Save the file
5. The changes will automatically reflect throughout the website

## Examples

### Adding a New Screenshot
```javascript
screenshots: [
  // ... existing screenshots
  {
    id: 9,
    alt: {
      en: 'ContentPal New Feature',
      zh: '内容君新功能',
      ja: 'ContentPalの新機能'
    }
  }
]
```

### Updating App Store URL
```javascript
stores: {
  appleStore: {
    url: 'https://apps.apple.com/app/contentpal/idNEWIDHERE', // Update this
    badge: '/assets/app-store-badge.svg'
  }
}
```

### Adding a New Social Media Link
```javascript
social: {
  twitter: 'https://twitter.com/contentpal',
  instagram: 'https://instagram.com/contentpal',
  linkedin: 'https://linkedin.com/company/contentpal',
  // Add new one
  youtube: 'https://youtube.com/@contentpal'
}
```

## Best Practices

1. **Always use helper functions** instead of directly accessing config values
2. **Keep URLs consistent** - use absolute URLs where possible
3. **Update all language versions** when updating translatable content
4. **Test changes** - after updating configuration, test the affected pages
5. **Document changes** - if you're working in a team, communicate configuration changes

## Components Using Configuration

- Header.jsx - App name, navigation links
- Footer.jsx - Contact info, legal links
- Home.jsx - SEO meta, store links, app description
- PrivacyPolicy.jsx - Contact information
- Documentation.jsx - Support contact info
- Screenshots.jsx - Screenshot configuration

## Future Enhancements

The configuration system can be extended to include:
- Theme configuration
- Feature flags
- A/B testing configurations
- Analytics settings
- API endpoints
- Third-party service configurations